# Routes System - Clean Architecture

## Overview
This routing system follows clean code principles with separation of concerns and minimal duplication.

## File Structure

### Core Files
- **`route_constants.dart`** - All route constants and patterns
- **`route_utils.dart`** - Utility functions for URL operations
- **`user_state_manager.dart`** - User state management utilities
- **`app_routes.dart`** - Main routes class (delegates to utils)
- **`route_generator.dart`** - Route generation logic
- **`route_middleware.dart`** - Web routing middleware

### Navigation
- **`Navigation/navigation_contract.dart`** - Navigation interface
- **`Navigation/navigation_service.dart`** - Navigation implementation
- **`Navigation/navigation_helper.dart`** - High-level navigation helpers

### Exports
- **`index.dart`** - Single import point for all routing functionality

## Usage

### Import
```dart
import 'package:devfolio/config/Routes/index.dart';
```

### Route Generation
```dart
// Static routes
AppRoutes.login
AppRoutes.dashboard

// Dynamic routes
AppRoutes.dashboardWithUser(email)
AppRoutes.portfolioByUsername(username)
```

### Navigation
```dart
// Navigate to user dashboard
NavigationHelper.navigateToDashboardWithUser();

// Handle username routes
NavigationHelper.handleDirectUsernameRoute(username);
```

### User State
```dart
// Check current user
UserStateManager.isCurrentUser(username);
UserStateManager.getCurrentUsername();
```

## URL Patterns

- `/` - Splash/Home
- `/login` - Login page
- `/register` - Registration
- `/dashboard` - Dashboard (requires auth)
- `/dashboard/username` - User-specific dashboard
- `/portfolio/username` - Public portfolio
- `/username` - Direct username access (auto-routes to dashboard/portfolio)

## Clean Code Principles Applied

1. **Single Responsibility** - Each file has one clear purpose
2. **DRY (Don't Repeat Yourself)** - Common logic extracted to utilities
3. **Separation of Concerns** - UI, logic, and state management separated
4. **Dependency Injection** - Services injected rather than directly accessed
5. **Clear Naming** - Self-documenting function and variable names
6. **Minimal Imports** - Only necessary dependencies imported

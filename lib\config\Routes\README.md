# 🗂️ Routes System - Clean Architecture

## 📋 Overview
This routing system follows clean code principles with separation of concerns and minimal duplication.

## 📁 File Structure

```text
Routes/
├── Constants/           # 🔢 Route constants and patterns
│   └── route_constants.dart
├── Utils/              # 🛠️ Utility functions for URL operations
│   └── route_utils.dart
├── State/              # 📊 User state management
│   └── user_state_manager.dart
├── Core/               # ⚡ Main routes logic
│   └── app_routes.dart
├── Generators/         # 🏭 Route generation logic
│   └── route_generator.dart
├── Middleware/         # 🌐 Web routing middleware
│   └── route_middleware.dart
├── Navigation/         # 🧭 Navigation services
│   ├── navigation_contract.dart
│   ├── navigation_service.dart
│   └── navigation_helper.dart
├── index.dart          # 📦 Single import point
└── README.md           # 📖 This file
```

---

## 📂 Detailed Folder Explanation

### 🔢 **Constants/**
**Purpose:** Static values and route patterns

#### `route_constants.dart`
```dart
class RouteConstants {
  // Static routes
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String dashboard = '/dashboard';
  static const String portfolio = '/portfolio';

  // Static route names for validation
  static const List<String> staticRoutes = [
    'login', 'register', 'dashboard', 'portfolio'
  ];

  // Route patterns
  static const String dashboardPattern = '/dashboard/';
  static const String portfolioPattern = '/portfolio/';
}
```

**What it does:**
- ✅ Centralizes all route constants
- ✅ Prevents typos in route names
- ✅ Makes route changes easier
- ✅ Provides validation lists

---

### 🛠️ **Utils/**
**Purpose:** Pure functions for URL manipulation

#### `route_utils.dart`
```dart
class RouteUtils {
  // Extract username from email
  static String getUsernameFromEmail(String email);

  // Build routes
  static String buildDashboardRoute(String userEmail);
  static String buildPortfolioRoute(String userEmail);

  // Parse URLs
  static String? extractUsernameFromRoute(String route);

  // Validation
  static bool isStaticRoute(String path);
  static bool isCurrentUserRoute(String route, String currentUserEmail);
}
```

**What it does:**
- ✅ Handles all URL parsing and building
- ✅ Extracts usernames from emails and URLs
- ✅ Validates routes and users
- ✅ Pure functions (no side effects)

---

### 📊 **State/**
**Purpose:** User state management and utilities

#### `user_state_manager.dart`
```dart
class UserStateManager {
  // Get user data
  static Map<String, dynamic>? getCurrentUserData();
  static String? getCurrentUserEmail();
  static String? getCurrentUsername();

  // Check user state
  static bool isUserLoggedIn();
  static bool isCurrentUser(String username);
  static bool hasUserData();
}
```

**What it does:**
- ✅ Centralizes user state access
- ✅ Provides clean API for user checks
- ✅ Abstracts UserDataService complexity
- ✅ Makes testing easier

---

### ⚡ **Core/**
**Purpose:** Main routing logic and route builders

#### `app_routes.dart`
```dart
class AppRoutes {
  // Static routes (delegates to RouteConstants)
  static const String login = RouteConstants.login;

  // Dynamic routes (delegates to RouteUtils)
  static String dashboardWithUser(String userEmail) {
    return RouteUtils.buildDashboardRoute(userEmail);
  }

  // Helper methods (delegates to RouteUtils)
  static String? extractUsernameFromRoute(String route) {
    return RouteUtils.extractUsernameFromRoute(route);
  }
}

class AppRouteBuilders {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    // Main routing logic here
  }
}
```

**What it does:**
- ✅ Main entry point for all routing
- ✅ Delegates to appropriate utilities
- ✅ Contains route generation logic
- ✅ Handles complex routing scenarios

---

### 🏭 **Generators/**
**Purpose:** Route generation and delegation

#### `route_generator.dart`
```dart
class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    return AppRouteBuilders.generateRoute(settings);
  }
}
```

**What it does:**
- ✅ Simple delegation to main route builder
- ✅ Keeps Flutter routing interface clean
- ✅ Single point of route generation

---

### 🌐 **Middleware/**
**Purpose:** Web-specific routing middleware

#### `route_middleware.dart`
```dart
class RouteMiddleware {
  // Initialize web routing
  static void initializeWebRouting();

  // Handle browser navigation
  static void _handleBrowserNavigation();

  // Route validation
  static bool shouldRedirectUser(String route);
  static String getCorrectRouteForUsername(String username);
}
```

**What it does:**
- ✅ Handles web-specific routing logic
- ✅ Manages browser navigation events
- ✅ Validates and redirects routes
- ✅ Only runs on web platform

---

### 🧭 **Navigation/**
**Purpose:** Navigation services and helpers

#### `navigation_contract.dart`
```dart
abstract class NavigationContract {
  Future<void> navigateTo(String route, {Object? arguments});
  Future<void> replaceWith(String route, {Object? arguments});
  Future<void> clearAndNavigateTo(String route);
  void goBack();
}
```

#### `navigation_service.dart`
```dart
class AppNavigationService implements NavigationContract {
  final GlobalKey<NavigatorState> navigatorKey;

  // Implementation of navigation methods
  Future<void> navigateTo(String route, {Object? arguments});
  Future<void> navigateToDashboardWithUser();
  Future<void> navigateToPortfolioWithUser();

  // Browser URL management
  void updateBrowserUrl(String route);
}
```

#### `navigation_helper.dart`
```dart
class NavigationHelper {
  // High-level navigation methods
  static Future<void> handleUsernameRoute(String username);
  static Future<void> handleDirectUsernameRoute(String username);
  static Future<void> navigateToDashboardWithUser();
  static Future<void> navigateToPortfolioWithUser();
  static Future<void> handleNoLocalData(String username);
}
```

**What they do:**
- ✅ **Contract:** Defines navigation interface
- ✅ **Service:** Implements low-level navigation
- ✅ **Helper:** Provides high-level navigation methods
- ✅ Handles complex navigation scenarios
- ✅ Manages browser URL updates

---

## 🎯 **Usage Examples**

### Import
```dart
import 'package:devfolio/config/Routes/index.dart';
```

### Route Generation
```dart
// Static routes
AppRoutes.login
AppRoutes.dashboard

// Dynamic routes
AppRoutes.dashboardWithUser(email)
AppRoutes.portfolioByUsername(username)
```

### Navigation
```dart
// Navigate to user dashboard
NavigationHelper.navigateToDashboardWithUser();

// Handle username routes
NavigationHelper.handleDirectUsernameRoute(username);
```

### User State
```dart
// Check current user
UserStateManager.isCurrentUser(username);
UserStateManager.getCurrentUsername();
```

---

## 🌐 **URL Patterns**

| Pattern | Description | Example |
|---------|-------------|---------|
| `/` | Splash/Home | `https://app.com/` |
| `/login` | Login page | `https://app.com/login` |
| `/register` | Registration | `https://app.com/register` |
| `/dashboard` | Dashboard (requires auth) | `https://app.com/dashboard` |
| `/dashboard/username` | User-specific dashboard | `https://app.com/dashboard/john` |
| `/portfolio/username` | Public portfolio | `https://app.com/portfolio/john` |
| `/username` | Direct username access | `https://app.com/john` |

---

## ✨ **Clean Code Principles Applied**

1. **🎯 Single Responsibility** - Each file has one clear purpose
2. **🔄 DRY (Don't Repeat Yourself)** - Common logic extracted to utilities
3. **🔀 Separation of Concerns** - UI, logic, and state management separated
4. **💉 Dependency Injection** - Services injected rather than directly accessed
5. **📝 Clear Naming** - Self-documenting function and variable names
6. **📦 Minimal Imports** - Only necessary dependencies imported
7. **🗂️ Organized Structure** - Logical folder organization
8. **🧪 Testable** - Pure functions and clear interfaces

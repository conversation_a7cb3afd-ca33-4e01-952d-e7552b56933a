# Routes System - Clean Architecture

## Overview
This routing system follows clean code principles with separation of concerns and minimal duplication.

## File Structure

```
Routes/
├── Constants/           # Route constants and patterns
│   └── route_constants.dart
├── Utils/              # Utility functions for URL operations
│   └── route_utils.dart
├── State/              # User state management
│   └── user_state_manager.dart
├── Core/               # Main routes logic
│   └── app_routes.dart
├── Generators/         # Route generation logic
│   └── route_generator.dart
├── Middleware/         # Web routing middleware
│   └── route_middleware.dart
├── Navigation/         # Navigation services
│   ├── navigation_contract.dart
│   ├── navigation_service.dart
│   └── navigation_helper.dart
├── index.dart          # Single import point
└── README.md           # This file
```

### Folder Purposes
- **`Constants/`** - Static values and route patterns
- **`Utils/`** - Pure functions for URL manipulation
- **`State/`** - User state management and utilities
- **`Core/`** - Main routing logic and route builders
- **`Generators/`** - Route generation and delegation
- **`Middleware/`** - Web-specific routing middleware
- **`Navigation/`** - Navigation services and helpers

## Usage

### Import
```dart
import 'package:devfolio/config/Routes/index.dart';
```

### Route Generation
```dart
// Static routes
AppRoutes.login
AppRoutes.dashboard

// Dynamic routes
AppRoutes.dashboardWithUser(email)
AppRoutes.portfolioByUsername(username)
```

### Navigation
```dart
// Navigate to user dashboard
NavigationHelper.navigateToDashboardWithUser();

// Handle username routes
NavigationHelper.handleDirectUsernameRoute(username);
```

### User State
```dart
// Check current user
UserStateManager.isCurrentUser(username);
UserStateManager.getCurrentUsername();
```

## URL Patterns

- `/` - Splash/Home
- `/login` - Login page
- `/register` - Registration
- `/dashboard` - Dashboard (requires auth)
- `/dashboard/username` - User-specific dashboard
- `/portfolio/username` - Public portfolio
- `/username` - Direct username access (auto-routes to dashboard/portfolio)

## Clean Code Principles Applied

1. **Single Responsibility** - Each file has one clear purpose
2. **DRY (Don't Repeat Yourself)** - Common logic extracted to utilities
3. **Separation of Concerns** - UI, logic, and state management separated
4. **Dependency Injection** - Services injected rather than directly accessed
5. **Clear Naming** - Self-documenting function and variable names
6. **Minimal Imports** - Only necessary dependencies imported

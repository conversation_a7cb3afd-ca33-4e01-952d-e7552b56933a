import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:web/web.dart' as web;
import '../Core/app_routes.dart';
import '../Utils/route_utils.dart';
import '../State/user_state_manager.dart';

class RouteMiddleware {
  static void initializeWebRouting() {
    if (kIsWeb) {
      _handleBrowserNavigation();
    }
  }

  /// Handle browser navigation events
  static void _handleBrowserNavigation() {
    try {
      final currentUrl = web.window.location.pathname;
      log('Browser navigation to: $currentUrl');

      // Extract username from URL if present
      final username = AppRoutes.extractUsernameFromRoute(currentUrl);

      if (username != null) {
        // Handle username-based routing
        _handleUsernameRoute(currentUrl, username);
      }
    } catch (e) {
      log('Error handling browser navigation: $e');
    }
  }

  /// Handle username-based routes
  static void _handleUsernameRoute(String route, String username) {
    try {
      final currentUserEmail = UserStateManager.getCurrentUserEmail();

      if (currentUserEmail != null) {
        final currentUsername = RouteUtils.getUsernameFromEmail(currentUserEmail);

        if (username == currentUsername) {
          // Same user - ensure we're on dashboard route
          if (route.startsWith('/portfolio/')) {
            // User typed their own username in portfolio URL - redirect to dashboard
            log('Redirecting user to their dashboard: $username');
            // We'll handle this in the route builder instead
            return;
          }
        } else {
          // Different user - ensure we're on portfolio route
          if (route.startsWith('/dashboard/')) {
            // User trying to access someone else's dashboard - redirect to portfolio
            log('Redirecting to portfolio for different user: $username');
            // We'll handle this in the route builder instead
            return;
          }
        }
      } else {
        // No user logged in - always redirect to portfolio
        if (route.startsWith('/dashboard/')) {
          log('No user logged in, redirecting to portfolio: $username');
          // We'll handle this in the route builder instead
          return;
        }
      }
    } catch (e) {
      log('Error in _handleUsernameRoute: $e');
    }
  }

  // /// Check if user should be redirected based on current route
  // static bool shouldRedirectUser(String route) {
  //   try {
  //     final username = AppRoutes.extractUsernameFromRoute(route);
  //     if (username == null) return false;

  //     final currentUserEmail = UserStateManager.getCurrentUserEmail();

  //     if (currentUserEmail != null) {
  //       final currentUsername = RouteUtils.getUsernameFromEmail(currentUserEmail);

  //       // If user is accessing their own portfolio URL, redirect to dashboard
  //       if (username == currentUsername && route.startsWith('/portfolio/')) {
  //         return true;
  //       }

  //       // If user is trying to access someone else's dashboard, redirect to portfolio
  //       if (username != currentUsername && route.startsWith('/dashboard/')) {
  //         return true;
  //       }
  //     } else {
  //       // No user logged in - redirect dashboard routes to portfolio
  //       if (route.startsWith('/dashboard/')) {
  //         return true;
  //       }
  //     }

  //     return false;
  //   } catch (e) {
  //     log('Error in shouldRedirectUser: $e');
  //     return false;
  //   }
  // }

  // /// Get the correct route for a username
  // static String getCorrectRouteForUsername(String username) {
  //   try {
  //     final currentUserEmail = UserStateManager.getCurrentUserEmail();
  //     return RouteUtils.getCorrectRouteForUsername(username, currentUserEmail);
  //   } catch (e) {
  //     log('Error in getCorrectRouteForUsername: $e');
  //     return AppRoutes.portfolioByUsername(username);
  //   }
  // }
}

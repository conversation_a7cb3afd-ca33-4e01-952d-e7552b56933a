// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';

// import 'Core/resources/resources.dart';
// import 'config/Routes/app_routes.dart';
// import 'config/Routes/route_generator.dart';
// import 'config/Themes/themes_app.dart';
// import 'config/cubit/portfolio_cubit.dart';
// import 'main.dart';

// class PortfolioApp extends StatelessWidget {
//   const PortfolioApp({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return ScreenUtilInit(
//       designSize: const Size(1920, 1080),
//       minTextAdapt: true,
//       splitScreenMode: true,
//       useInheritedMediaQuery: true,
//       enableScaleWH: () => false,
//       enableScaleText: () => true,

//       builder: (context, child) {
//         return BlocProvider(
//           create: (context) => PortfolioCubit(),
//           child: MaterialApp(
//             title: AppStrings.appTitle,
//             debugShowCheckedModeBanner: false,
//             theme: themesApp(context),
//              initialRoute: AppRoutes.portfolio,
//             onGenerateRoute: RouteGenerator.generateRoute,
//             navigatorKey: kNavigationService.navigatorKey,
//           ),
//         );
//       },
//     );
//   }


// }

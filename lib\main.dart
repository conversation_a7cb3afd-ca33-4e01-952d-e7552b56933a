import 'dart:developer';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_web_plugins/url_strategy.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

import 'Core/Storage/Local/UserDataService/user_data_base_service.dart';
import 'Core/services/Subabase/subabase_services.dart';
import 'auth_app.dart';
import 'config/Routes/Navigation/navigation_service.dart';
import 'config/Routes/route_middleware.dart';
import 'config/subabase_keys.dart';

Future<void> main() async {
 
    WidgetsFlutterBinding.ensureInitialized();
      usePathUrlStrategy();
    // if (kIsWeb) {
    //   usePathUrlStrategy();
    //   FlutterError.onError = (FlutterErrorDetails details) {
    //     log('FlutterError: ${details.exception.toString()}');
    //     if (details.exception.toString().contains('history')) {
    //       return;
    //     }
    //     FlutterError.presentError(details);
    //   };
    // }

    await Hive.initFlutter();
    await UserDataBaseService.init();
    await Supabase.initialize(
      url: SupabaseKeys.url,
      anonKey: SupabaseKeys.apiKey,
    );
    await SubabaseServices.init();

    if (kIsWeb) {
      RouteMiddleware.initializeWebRouting();
    }

    runApp(const AuthApp());

}

final AppNavigationService kNavigationService = AppNavigationService();

import 'package:flutter/material.dart';
import '../../../../main.dart';

/// Button widget for navigating from Dashboard to Portfolio
/// This will pass the full PortfolioDataModel as arguments
class PortfolioNavigationButton extends StatelessWidget {
  const PortfolioNavigationButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton.icon(
      onPressed: () {
        // Navigate to portfolio with full data from dashboard
        kNavigationService.navigateToPortfolioFromDashboard();
      },
      icon: const Icon(Icons.visibility),
      label: const Text('View Portfolio'),
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      ),
    );
  }
}

/// Example of how to use in Dashboard
class DashboardPortfolioSection extends StatelessWidget {
  const DashboardPortfolioSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Portfolio Preview',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            const Text(
              'See how your portfolio looks to visitors',
              style: TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            const PortfolioNavigationButton(),
          ],
        ),
      ),
    );
  }
}

import 'dart:developer';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:web/web.dart' as web;
import 'navigation_contract.dart';
import '../app_routes.dart';
import '../user_state_manager.dart';
import 'navigation_helper.dart';

class AppNavigationService implements NavigationContract {
  final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();
  List<String> navigationHistory = [];

  @override
  Future<void> navigateTo(String route, {Object? arguments}) async {
    updateBrowserUrl(route);
    await navigatorKey.currentState?.pushNamed(route, arguments: arguments);
  }

  @override
  Future<void> replaceWith(String route, {Object? arguments}) async {
    if (navigationHistory.isNotEmpty) {
      navigationHistory.removeLast();
    }
    updateBrowserUrl(route);

    await navigatorKey.currentState?.pushReplacementNamed(
      route,
      arguments: arguments,
    );
  }

  @override
  Future<void> clearAndNavigateTo(String route, {Object? arguments}) async {
    navigationHistory.clear();
    updateBrowserUrl(route);

    await navigatorKey.currentState?.pushNamedAndRemoveUntil(
      route,
      (Route<dynamic> r) => false,
      arguments: arguments,
    );
  }

  @override
  Future<void> goBack() async {
    try {
      log('navigationHistory 1: ${navigationHistory.toList()}');
      if (navigationHistory.isNotEmpty) {
        navigationHistory.removeLast();
      }
      log('navigationHistory 2: ${navigationHistory.toList()}');

      if (navigationHistory.isNotEmpty) {
        _updateBrowserUrlImmediately(navigationHistory.last);
      }
      if (navigatorKey.currentState?.canPop() ?? false) {
        navigatorKey.currentState?.pop();
      }
    } catch (e) {
      log('Error in goBack: $e');
      // Fallback: just pop without updating history
      if (navigatorKey.currentState?.canPop() ?? false) {
        navigatorKey.currentState?.pop();
      }
    }
  }

  void _updateBrowserUrlImmediately(String route) {
    try {
      // Ensure we're running on web and have access to history API
      if (kIsWeb) {
        // Clean the route to ensure it starts with /
        final cleanRoute = route.startsWith('/') ? route : '/$route';

        // Create the full URL
        final currentUrl = web.window.location.href;
        final uri = Uri.parse(currentUrl);
        final newUri = uri.replace(path: cleanRoute);

        // Update the browser URL without triggering navigation
        web.window.history.replaceState(null, '', newUri.toString());
        log('Browser URL updated to: ${newUri.toString()}');
      }
    } catch (e) {
      log('Error updating browser URL: $e');
    }
  }

  void updateBrowserUrl(String route) {
    try {
      navigationHistory.add(route);
      // Use a safer approach for updating URL
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _updateBrowserUrlImmediately(route);
      });
    } catch (e) {
      log('Error in updateBrowserUrl: $e');
    }
  }

  // Navigate to dashboard with user email in URL
  Future<void> navigateToDashboardWithUser() async {
    try {
      final userEmail = UserStateManager.getCurrentUserEmail();
      if (userEmail != null) {
        final route = AppRoutes.dashboardWithUser(userEmail);
        await clearAndNavigateTo(route);
      } else {
        // Fallback to regular dashboard
        await clearAndNavigateTo(AppRoutes.dashboard);
      }
    } catch (e) {
      log('Error navigating to dashboard with user: $e');
      // Fallback to regular dashboard
      await clearAndNavigateTo(AppRoutes.dashboard);
    }
  }

  // Navigate to portfolio with user email in URL
  Future<void> navigateToPortfolioWithUser() async {
    try {
      final userEmail = UserStateManager.getCurrentUserEmail();
      if (userEmail != null) {
        final route = AppRoutes.portfolioWithUser(userEmail);
        await clearAndNavigateTo(route);
      } else {
        // Fallback to regular portfolio
        await clearAndNavigateTo(AppRoutes.portfolio);
      }
    } catch (e) {
      log('Error navigating to portfolio with user: $e');
      // Fallback to regular portfolio
      await clearAndNavigateTo(AppRoutes.portfolio);
    }
  }

  // Handle username-based navigation with database verification
  Future<void> handleUsernameNavigation(
    String username, {
    bool forcePortfolio = false,
  }) async {
    // Delegate to NavigationHelper for comprehensive handling
    await NavigationHelper.handleUsernameRoute(
      username,
      forcePortfolio: forcePortfolio,
    );
  }

  // Navigate with arguments
  Future<void> clearAndNavigateToWithArguments(
    String route, {
    Object? arguments,
  }) async {
    try {
      navigationHistory.clear();
      updateBrowserUrl(route);
      await navigatorKey.currentState?.pushNamedAndRemoveUntil(
        route,
        (route) => false,
        arguments: arguments,
      );
    } catch (e) {
      log('Error in clearAndNavigateToWithArguments: $e');
    }
  }

  // Navigate to portfolio with full data (from dashboard)
  Future<void> navigateToPortfolioWithData(
    String username,
    dynamic portfolioData,
  ) async {
    try {
      final route = AppRoutes.portfolioByUsername(username);
      await clearAndNavigateToWithArguments(
        route,
        arguments: portfolioData, // Pass full PortfolioDataModel
      );
    } catch (e) {
      log('Error navigating to portfolio with data: $e');
      // Fallback to regular portfolio
      final route = AppRoutes.portfolioByUsername(username);
      await clearAndNavigateTo(route);
    }
  }

  // Navigate to portfolio from dashboard (with full data)
  Future<void> navigateToPortfolioFromDashboard() async {
    // Delegate to NavigationHelper
    await NavigationHelper.navigateToPortfolioFromDashboard();
  }
}

{"buildCommand": "flutter build web --release", "outputDirectory": "build/web", "installCommand": "if [ -e pubspec.yaml ]; then dart pub get; fi", "framework": null, "rewrites": [{"source": "/(.*)", "destination": "/index.html"}], "headers": [{"source": "/index.html", "headers": [{"key": "Cache-Control", "value": "no-cache, no-store, must-revalidate"}]}, {"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}], "functions": {"build/web/**": {"includeFiles": "build/web/**"}}}
# 🚀 Routes Quick Guide

## 📦 Single Import
```dart
import 'package:devfolio/config/Routes/index.dart';
```

## 🗂️ Folder Structure
```
Routes/
├── Constants/      # 🔢 Static route values
├── Utils/          # 🛠️ URL manipulation functions  
├── State/          # 📊 User state management
├── Core/           # ⚡ Main routing logic
├── Generators/     # 🏭 Route generation
├── Middleware/     # 🌐 Web routing middleware
└── Navigation/     # 🧭 Navigation services
```

## 🎯 Common Usage

### Routes
```dart
// Static routes
AppRoutes.login           // '/login'
AppRoutes.dashboard       // '/dashboard'

// Dynamic routes  
AppRoutes.dashboardWithUser(email)     // '/dashboard/username'
AppRoutes.portfolioByUsername(username) // '/portfolio/username'
```

### Navigation
```dart
// Navigate to user dashboard
NavigationHelper.navigateToDashboardWithUser();

// Handle direct username routes
NavigationHelper.handleDirectUsernameRoute('john');
```

### User State
```dart
// Check current user
UserStateManager.isCurrentUser('john');        // true/false
UserStateManager.getCurrentUsername();         // 'john'
UserStateManager.isUserLoggedIn();            // true/false
```

### URL Utils
```dart
// Extract username from email
RouteUtils.getUsernameFromEmail('<EMAIL>');  // 'john'

// Extract username from URL
RouteUtils.extractUsernameFromRoute('/dashboard/john');  // 'john'

// Check if static route
RouteUtils.isStaticRoute('login');  // true
```

## 🌐 URL Patterns
| URL | Goes To | Example |
|-----|---------|---------|
| `/john` | Dashboard (if john is logged in) or Portfolio | Auto-detect |
| `/dashboard/john` | John's Dashboard | If john is logged in |
| `/portfolio/john` | John's Public Portfolio | Always accessible |
| `/login` | Login Page | Static route |

## ✨ Key Benefits
- 🎯 **Single Responsibility** - Each folder has one purpose
- 🔄 **No Duplication** - Shared logic in utils
- 📦 **Easy Import** - One import for everything
- 🧪 **Testable** - Pure functions and clear interfaces
- 🗂️ **Organized** - Logical folder structure

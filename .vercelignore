# Flutter build artifacts
.dart_tool/
.packages
.pub-cache/
.pub/
# Keep build/web for deployment
build/android/
build/ios/
build/linux/
build/macos/
build/windows/

# IDE files
.vscode/
.idea/
*.iml
*.ipr
*.iws

# OS files
.DS_Store
Thumbs.db

# Local development
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Dependencies
node_modules/

# Test files
test/
coverage/

# Documentation
doc/
docs/
README.md

# Git
.git/
.gitignore

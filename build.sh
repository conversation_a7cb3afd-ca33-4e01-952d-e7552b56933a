#!/bin/bash

# Flutter Web Build Script for Vercel

echo "Starting Flutter web build..."

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "Flutter not found. Installing Flutter..."
    # This will be handled by Vercel's Flutter buildpack
    exit 1
fi

# Get Flutter dependencies
echo "Getting Flutter dependencies..."
flutter pub get

# Clean previous builds
echo "Cleaning previous builds..."
flutter clean

# Build for web with release mode
echo "Building Flutter web app..."
flutter build web --release --web-renderer html --base-href /

# Copy redirect files to build directory
echo "Copying redirect files..."
cp web/_redirects build/web/_redirects 2>/dev/null || echo "No _redirects file found in web/"

# Create _redirects file if it doesn't exist
if [ ! -f "build/web/_redirects" ]; then
    echo "Creating _redirects file..."
    echo "/*    /index.html   200" > build/web/_redirects
fi

# Verify build output
if [ -d "build/web" ]; then
    echo "Build successful! Output directory: build/web"
    ls -la build/web/
    echo "Redirect file content:"
    cat build/web/_redirects
else
    echo "Build failed! No output directory found."
    exit 1
fi

echo "Flutter web build completed successfully!"

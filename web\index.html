<!DOCTYPE html>
<html>
<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="Professional Portfolio - Showcase your skills and projects">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">

  <!-- Open Graph / Facebook -->
  <meta property="og:type" content="website">
  <meta property="og:title" content="DevFolio - Professional Portfolio">
  <meta property="og:description" content="Professional Portfolio - Showcase your skills and projects">

  <!-- Twitter -->
  <meta property="twitter:card" content="summary_large_image">
  <meta property="twitter:title" content="DevFolio - Professional Portfolio">
  <meta property="twitter:description" content="Professional Portfolio - Showcase your skills and projects">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="devfolio">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>DevFolio - Professional Portfolio</title>
  <link rel="manifest" href="manifest.json">

  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #0a0a0a;
      color: white;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    .loading {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: #0a0a0a;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
      z-index: 9999;
    }

    .spinner {
      width: 50px;
      height: 50px;
      border: 3px solid #333;
      border-top: 3px solid #6366F1;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }

    .loading-text {
      margin-top: 20px;
      color: #6366F1;
      font-size: 16px;
    }
  </style>
</head>
<body>
  <!-- Loading Screen -->
  <div id="loading" class="loading">
    <div class="spinner"></div>
    <div class="loading-text">Loading Portfolio...</div>
  </div>

  <script>
    // Hide loading screen when Flutter app is ready
    window.addEventListener('flutter-first-frame', function () {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    });

    // Fallback: Hide loading screen after 10 seconds
    setTimeout(function() {
      const loading = document.getElementById('loading');
      if (loading) {
        loading.style.display = 'none';
      }
    }, 10000);
  </script>

  <script src="flutter_bootstrap.js" async></script>
</body>
</html>

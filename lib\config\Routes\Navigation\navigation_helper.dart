import 'dart:developer';
import '../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../../Core/services/user_verification_service.dart';
import '../app_routes.dart';
import '../route_utils.dart';
import 'navigation_service.dart';

class NavigationHelper {
  static final AppNavigationService _navigationService = AppNavigationService();

  /// Handle navigation based on username in URL
  /// If username matches current user -> Dashboard
  /// If username is different or no user logged in -> Portfolio
  static Future<void> handleUsernameRoute(
    String username, {
    bool forcePortfolio = false,
  }) async {
    try {
      final userData = UserDataService.getUserData();

      if (userData != null &&
          userData['emailUser'] != null &&
          !forcePortfolio) {
        final currentUserEmail = userData['emailUser'] as String;
        final currentUsername = currentUserEmail.split('@').first;

        if (username == currentUsername) {
          // Same user - go to dashboard
          log('Same user detected: $username, navigating to dashboard');
          final route = AppRoutes.dashboardWithUser(currentUserEmail);
          await _navigationService.clearAndNavigateTo(route);
          return;
        }
      }

      // No local data or different user - check database
      log('Checking database for user: $username');
      final result = await UserVerificationService.checkUserByUsername(
        username,
      );

      await result.fold(
        (error) async {
          // Database error - redirect to login
          log('Database error for user $username: $error');
          await _navigationService.clearAndNavigateTo(AppRoutes.login);
        },
        (portfolioData) async {
          if (portfolioData != null) {
            // User exists in database
            if (userData == null || userData['emailUser'] == null) {
              // No local user data - check if this should be dashboard or portfolio
              log('User $username exists in database, showing portfolio');
              final route = AppRoutes.portfolioByUsername(username);
              await _navigationService.clearAndNavigateToWithArguments(
                route,
                arguments: portfolioData.email, // Pass email for direct access
              );
            } else {
              // Local user exists but different user - show portfolio
              log(
                'Different user $username exists in database, showing portfolio',
              );
              final route = AppRoutes.portfolioByUsername(username);
              await _navigationService.clearAndNavigateToWithArguments(
                route,
                arguments: portfolioData.email, // Pass email for direct access
              );
            }
          } else {
            // User doesn't exist in database - redirect to login
            log('User $username not found in database, redirecting to login');
            await _navigationService.clearAndNavigateTo(AppRoutes.login);
          }
        },
      );
    } catch (e) {
      log('Error in handleUsernameRoute: $e');
      // Fallback to login for safety
      await _navigationService.clearAndNavigateTo(AppRoutes.login);
    }
  }

  /// Navigate to dashboard with current user's username
  static Future<void> navigateToDashboardWithUser() async {
    try {
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final userEmail = userData['emailUser'] as String;
        final route = AppRoutes.dashboardWithUser(userEmail);
        await _navigationService.clearAndNavigateTo(route);
      } else {
        // Fallback to regular dashboard
        await _navigationService.clearAndNavigateTo(AppRoutes.dashboard);
      }
    } catch (e) {
      log('Error navigating to dashboard with user: $e');
      // Fallback to regular dashboard
      await _navigationService.clearAndNavigateTo(AppRoutes.dashboard);
    }
  }

  /// Navigate to portfolio with current user's username
  static Future<void> navigateToPortfolioWithUser() async {
    try {
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final userEmail = userData['emailUser'] as String;
        final route = AppRoutes.portfolioWithUser(userEmail);
        await _navigationService.clearAndNavigateTo(route);
      } else {
        // Fallback to regular portfolio
        await _navigationService.clearAndNavigateTo(AppRoutes.portfolio);
      }
    } catch (e) {
      log('Error navigating to portfolio with user: $e');
      // Fallback to regular portfolio
      await _navigationService.clearAndNavigateTo(AppRoutes.portfolio);
    }
  }

  /// Navigate to portfolio from dashboard with full portfolio data
  static Future<void> navigateToPortfolioFromDashboard() async {
    try {
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final userEmail = userData['emailUser'] as String;

        // Get full portfolio data from local storage or database
        final portfolioDataMap = UserDataService.getUserData();
        if (portfolioDataMap != null) {
          final route = AppRoutes.portfolioWithUser(userEmail);
          await _navigationService.clearAndNavigateToWithArguments(
            route,
            arguments: portfolioDataMap, // Pass full PortfolioDataModel
          );
        } else {
          // Fallback to regular navigation
          await navigateToPortfolioWithUser();
        }
      } else {
        // Fallback to regular portfolio
        await _navigationService.clearAndNavigateTo(AppRoutes.portfolio);
      }
    } catch (e) {
      log('Error navigating to portfolio from dashboard: $e');
      // Fallback to regular portfolio
      await _navigationService.clearAndNavigateTo(AppRoutes.portfolio);
    }
  }

  /// Check if current route matches current user
  static bool isCurrentUserRoute(String route) {
    try {
      final userData = UserDataService.getUserData();
      if (userData != null && userData['emailUser'] != null) {
        final currentUserEmail = userData['emailUser'] as String;
        return AppRoutes.isCurrentUserRoute(route, currentUserEmail);
      }
      return false;
    } catch (e) {
      log('Error checking current user route: $e');
      return false;
    }
  }

  /// Get current user's username
  static String? getCurrentUsername() {
    try {
      final userData = UserDataService.getUserData();
      final userEmail = userData?['emailUser'] as String?;
      return userEmail != null
          ? RouteUtils.getUsernameFromEmail(userEmail)
          : null;
    } catch (e) {
      log('Error getting current username: $e');
      return null;
    }
  }

  /// Handle case when no local data exists - check database and navigate accordingly
  static Future<void> handleNoLocalData(String username) async {
    try {
      log('No local data found, checking database for user: $username');

      final result = await UserVerificationService.checkUserByUsername(
        username,
      );

      await result.fold(
        (error) async {
          // Database error - redirect to login
          log('Database error when checking user $username: $error');
          await _navigationService.clearAndNavigateTo(AppRoutes.login);
        },
        (portfolioData) async {
          if (portfolioData != null) {
            // User exists in database - show portfolio
            log('User $username found in database, showing portfolio');
            final route = AppRoutes.portfolioByUsername(username);
            await _navigationService.clearAndNavigateTo(route);
          } else {
            // User doesn't exist - redirect to login
            log('User $username not found in database, redirecting to login');
            await _navigationService.clearAndNavigateTo(AppRoutes.login);
          }
        },
      );
    } catch (e) {
      log('Error in handleNoLocalData: $e');
      // Fallback to login
      await _navigationService.clearAndNavigateTo(AppRoutes.login);
    }
  }

  /// Verify and potentially auto-login user if they exist in database
  static Future<bool> verifyAndAutoLogin(String username) async {
    try {
      log('Attempting to verify and auto-login user: $username');

      final result = await UserVerificationService.checkUserByUsername(
        username,
      );

      return await result.fold(
        (error) async {
          log('Error verifying user $username: $error');
          return false;
        },
        (portfolioData) async {
          if (portfolioData != null) {
            // User exists - save to local storage for future use
            await UserVerificationService.saveUserDataLocally(portfolioData);
            log('User $username verified and data saved locally');
            return true;
          } else {
            log('User $username not found in database');
            return false;
          }
        },
      );
    } catch (e) {
      log('Error in verifyAndAutoLogin: $e');
      return false;
    }
  }

  /// Handle direct username routes (like /felopaters37)
  static Future<void> handleDirectUsernameRoute(String username) async {
    try {
      log('Handling direct username route: /$username');

      final userData = UserDataService.getUserData();

      // Check if this is the current logged-in user
      if (userData != null && userData['emailUser'] != null) {
        final currentUserEmail = userData['emailUser'] as String;
        final currentUsername = currentUserEmail.split('@').first;

        if (username.toLowerCase() == currentUsername.toLowerCase()) {
          // Same user - redirect to dashboard with proper URL
          log(
            'Current user accessing their own username route, redirecting to dashboard',
          );
          final route = AppRoutes.dashboardWithUser(currentUserEmail);
          await _navigationService.clearAndNavigateTo(route);
          return;
        }
      }

      // Different user or no user logged in - check database and show portfolio
      log('Checking database for username: $username');
      final result = await UserVerificationService.checkUserByUsername(
        username,
      );

      await result.fold(
        (error) async {
          // Database error - redirect to login
          log('Database error for username $username: $error');
          await _navigationService.clearAndNavigateTo(AppRoutes.login);
        },
        (portfolioData) async {
          if (portfolioData != null) {
            // User exists - show portfolio with proper URL and email
            log('User $username found in database, redirecting to portfolio');
            final route = AppRoutes.portfolioByUsername(username);
            await _navigationService.clearAndNavigateToWithArguments(
              route,
              arguments:
                  portfolioData.email, // Pass email for direct portfolio access
            );
          } else {
            // User doesn't exist - redirect to login
            log('User $username not found in database, redirecting to login');
            await _navigationService.clearAndNavigateTo(AppRoutes.login);
          }
        },
      );
    } catch (e) {
      log('Error in handleDirectUsernameRoute: $e');
      // Fallback to login
      await _navigationService.clearAndNavigateTo(AppRoutes.login);
    }
  }
}

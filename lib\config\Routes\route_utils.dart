import 'dart:developer';
import 'route_constants.dart';

/// Utility class for route operations
class RouteUtils {
  /// Extract username from email
  static String getUsernameFromEmail(String email) {
    return email.split('@').first;
  }

  /// Build dashboard route with user email
  static String buildDashboardRoute(String userEmail) {
    final username = getUsernameFromEmail(userEmail);
    return '${RouteConstants.dashboardPattern}$username';
  }

  /// Build portfolio route with user email
  static String buildPortfolioRoute(String userEmail) {
    final username = getUsernameFromEmail(userEmail);
    return '${RouteConstants.portfolioPattern}$username';
  }

  /// Build portfolio route with username directly
  static String buildPortfolioRouteByUsername(String username) {
    return '${RouteConstants.portfolioPattern}$username';
  }

  /// Extract username from URL route
  static String? extractUsernameFromRoute(String route) {
    log('Extracting username from route: $route');
    
    // Handle dashboard/portfolio routes
    if (route.startsWith(RouteConstants.dashboardPattern) || 
        route.startsWith(RouteConstants.portfolioPattern)) {
      final parts = route.split('/');
      if (parts.length >= 3) {
        log('Found username in dashboard/portfolio route: ${parts[2]}');
        return parts[2];
      }
    }
    
    // Handle direct username routes (like /felopaters37)
    if (route.startsWith('/') && route.length > 1) {
      final parts = route.split('/');
      if (parts.length == 2 && parts[1].isNotEmpty) {
        final username = parts[1];
        if (!isStaticRoute(username)) {
          log('Found direct username route: $username');
          return username;
        } else {
          log('Route is a static route, not a username: $username');
        }
      }
    }
    
    log('No username found in route: $route');
    return null;
  }

  /// Check if a path is a static route
  static bool isStaticRoute(String path) {
    return RouteConstants.staticRoutes.contains(path.toLowerCase());
  }

  /// Check if route is for current user
  static bool isCurrentUserRoute(String route, String currentUserEmail) {
    final usernameFromRoute = extractUsernameFromRoute(route);
    final currentUsername = getUsernameFromEmail(currentUserEmail);
    return usernameFromRoute?.toLowerCase() == currentUsername.toLowerCase();
  }

  /// Get correct route for username based on current user
  static String getCorrectRouteForUsername(String username, String? currentUserEmail) {
    if (currentUserEmail != null) {
      final currentUsername = getUsernameFromEmail(currentUserEmail);
      if (username.toLowerCase() == currentUsername.toLowerCase()) {
        return buildDashboardRoute(currentUserEmail);
      }
    }
    return buildPortfolioRouteByUsername(username);
  }
}

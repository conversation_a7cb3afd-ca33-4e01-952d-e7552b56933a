import 'dart:developer';
import 'package:flutter/material.dart';

import '../../Core/models/portfolio_data_model.dart';
import '../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../Features/Admin/Main_Dashboard/Presentation/Page/dashboard_page.dart';
import '../../Features/Auth/Presentation/Pages/login_page.dart';
import '../../Features/Auth/Presentation/Pages/register_page.dart';
import '../../Features/Portfolio/portfolio_main.dart';
import 'Navigation/navigation_helper.dart';
import 'route_constants.dart';
import 'route_utils.dart';
import '../../main.dart';

class AppRoutes {
  // Static routes - using constants from RouteConstants
  static const String splash = RouteConstants.splash;
  static const String login = RouteConstants.login;
  static const String register = RouteConstants.register;
  static const String dashboard = RouteConstants.dashboard;
  static const String portfolio = RouteConstants.portfolio;

  // Dynamic routes with user info - using RouteUtils
  static String dashboardWithUser(String userEmail) {
    return RouteUtils.buildDashboardRoute(userEmail);
  }

  static String portfolioWithUser(String userEmail) {
    return RouteUtils.buildPortfolioRoute(userEmail);
  }

  static String portfolioByUsername(String username) {
    return RouteUtils.buildPortfolioRouteByUsername(username);
  }

  // Helper methods - delegating to RouteUtils
  static String? extractUsernameFromRoute(String route) {
    return RouteUtils.extractUsernameFromRoute(route);
  }

  static bool isStaticRoute(String path) {
    return RouteUtils.isStaticRoute(path);
  }

  // Helper method to check if route is for current user
  static bool isCurrentUserRoute(String route, String currentUserEmail) {
    return RouteUtils.isCurrentUserRoute(route, currentUserEmail);
  }
}

class AppRouteBuilders {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routeName = settings.name ?? '';

    // Handle dynamic routes with username
    if (routeName.startsWith('/dashboard/')) {
      final username = AppRoutes.extractUsernameFromRoute(routeName);
      if (username != null) {
        // Check if this is the current user
        return _handleUserRoute(username, isPortfolio: false);
      }
      return _defaultPageRoute(const AdminDashboard());
    } else if (routeName.startsWith('/portfolio/')) {
      final username = AppRoutes.extractUsernameFromRoute(routeName);
      if (username != null) {
        // Always show portfolio for any username
        return _defaultPageRoute(const PortfolioMain());
      }
      return _defaultPageRoute(const PortfolioMain());
    }

    // Handle username directly in root (like /felopaters37)
    final directUsername = AppRoutes.extractUsernameFromRoute(routeName);
    if (directUsername != null && !AppRoutes.isStaticRoute(directUsername)) {
      log('Direct username route detected: $directUsername');

      // Check if this is the current user to decide between dashboard and portfolio
      final userData = UserDataService.getUserData();
      final currentUserEmail = userData?['emailUser'] as String?;
      final isCurrentUser =
          currentUserEmail != null &&
          RouteUtils.isCurrentUserRoute('/$directUsername', currentUserEmail);

      log(
        'Current user email: $currentUserEmail, Route user: $directUsername, Is same: $isCurrentUser',
      );

      // If it's the current user, go to dashboard; otherwise, portfolio
      log(
        'Navigating to ${isCurrentUser ? 'dashboard' : 'portfolio'} for user: $directUsername',
      );
      return _handleUserRoute(directUsername, isPortfolio: !isCurrentUser);
    }

    // Handle static routes
    switch (routeName) {
      case AppRoutes.login:
        return _defaultPageRoute(const LoginPage());
      case AppRoutes.register:
        return _defaultPageRoute(const RegisterPage());
      case AppRoutes.dashboard:
        return _defaultPageRoute(const AdminDashboard());
      case AppRoutes.portfolio:
        // Handle different argument types
        String? email;
        PortfolioDataModel? portfolioData;

        if (settings.arguments is String) {
          email = settings.arguments as String;
        } else if (settings.arguments is PortfolioDataModel) {
          portfolioData = settings.arguments as PortfolioDataModel;
          email = portfolioData.email;
        }

        return _defaultPageRoute(
          PortfolioMain(email: email, portfolioData: portfolioData),
        );
      default:
        return _errorRoute('No route defined for "$routeName"');
    }
  }

  static Route<dynamic> _handleUserRoute(
    String username, {
    required bool isPortfolio,
  }) {
    log(
      '_handleUserRoute called for username: $username, isPortfolio: $isPortfolio',
    );

    // Handle the navigation logic based on username
    Future.microtask(() {
      if (isPortfolio) {
        // This is a portfolio route - check database for user
        log('Handling portfolio route for username: $username');
        NavigationHelper.handleDirectUsernameRoute(username);
      } else {
        // This is a dashboard route for current user
        log('Handling dashboard route for current user: $username');
        final userData = UserDataService.getUserData();
        if (userData != null && userData['emailUser'] != null) {
          final currentUserEmail = userData['emailUser'] as String;
          final route = AppRoutes.dashboardWithUser(currentUserEmail);
          kNavigationService.clearAndNavigateTo(route);
        } else {
          // Fallback to regular dashboard
          kNavigationService.clearAndNavigateTo(AppRoutes.dashboard);
        }
      }
    });

    // Return the appropriate page immediately
    if (isPortfolio) {
      return _defaultPageRoute(const PortfolioMain());
    } else {
      return _defaultPageRoute(const AdminDashboard());
    }
  }

  static MaterialPageRoute<dynamic> _defaultPageRoute(Widget screen) {
    return MaterialPageRoute(builder: (_) => screen);
  }

  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body: Center(
          child: Text(message, style: const TextStyle(fontSize: 18)),
        ),
      ),
    );
  }
}

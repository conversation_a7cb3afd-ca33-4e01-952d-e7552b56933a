import 'dart:developer';
import 'package:flutter/material.dart';

import '../../Core/models/portfolio_data_model.dart';
import '../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../../Features/Admin/Main_Dashboard/Presentation/Page/dashboard_page.dart';
import '../../Features/Auth/Presentation/Pages/login_page.dart';
import '../../Features/Auth/Presentation/Pages/register_page.dart';
import '../../Features/Portfolio/portfolio_main.dart';
import 'Navigation/navigation_helper.dart';
import '../../main.dart';

class AppRoutes {
  static const String splash = '/';
  static const String login = '/login';
  static const String register = '/register';
  static const String dashboard = '/dashboard';
  static const String portfolio = '/portfolio';

  // Dynamic routes with user info
  static String dashboardWithUser(String userEmail) {
    // Extract username (part before @)
    final username = userEmail.split('@').first;
    return '/dashboard/$username';
  }

  static String portfolioWithUser(String userEmail) {
    // Extract username (part before @)
    final username = userEmail.split('@').first;
    return '/portfolio/$username';
  }

  // Route for accessing portfolio by username directly
  static String portfolioByUsername(String username) {
    return '/portfolio/$username';
  }

  // Helper method to extract username from URL
  static String? extractUsernameFromRoute(String route) {
    log('Extracting username from route: $route');

    if (route.startsWith('/dashboard/') || route.startsWith('/portfolio/')) {
      final parts = route.split('/');
      if (parts.length >= 3) {
        log('Found username in dashboard/portfolio route: ${parts[2]}');
        return parts[2]; // Return username directly
      }
    }
    // Check if route is just a username (like /felopaters37)
    else if (route.startsWith('/') && route.length > 1) {
      final parts = route.split('/');
      if (parts.length == 2 && parts[1].isNotEmpty) {
        // Check if it's not a known static route
        final username = parts[1];
        if (!_isStaticRoute(username)) {
          log('Found direct username route: $username');
          return username;
        } else {
          log('Route is a static route, not a username: $username');
        }
      }
    }
    log('No username found in route: $route');
    return null;
  }

  // Helper to check if a path is a static route
  static bool _isStaticRoute(String path) {
    const staticRoutes = ['login', 'register', 'dashboard', 'portfolio'];
    return staticRoutes.contains(path.toLowerCase());
  }

  // Helper method to check if route is for current user
  static bool isCurrentUserRoute(String route, String currentUserEmail) {
    final usernameFromRoute = extractUsernameFromRoute(route);
    final currentUsername = currentUserEmail.split('@').first;
    return usernameFromRoute == currentUsername;
  }
}

class AppRouteBuilders {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    final routeName = settings.name ?? '';

    // Handle dynamic routes with username
    if (routeName.startsWith('/dashboard/')) {
      final username = AppRoutes.extractUsernameFromRoute(routeName);
      if (username != null) {
        // Check if this is the current user
        return _handleUserRoute(username, isPortfolio: false);
      }
      return _defaultPageRoute(const AdminDashboard());
    } else if (routeName.startsWith('/portfolio/')) {
      final username = AppRoutes.extractUsernameFromRoute(routeName);
      if (username != null) {
        // Always show portfolio for any username
        return _defaultPageRoute(const PortfolioMain());
      }
      return _defaultPageRoute(const PortfolioMain());
    }

    // Handle username directly in root (like /felopaters37)
    final directUsername = AppRoutes.extractUsernameFromRoute(routeName);
    if (directUsername != null && !AppRoutes._isStaticRoute(directUsername)) {
      log('Direct username route detected: $directUsername');

      // Check if this is the current user to decide between dashboard and portfolio
      final userData = UserDataService.getUserData();
      bool isCurrentUser = false;

      if (userData != null && userData['emailUser'] != null) {
        final currentUserEmail = userData['emailUser'] as String;
        final currentUsername = currentUserEmail.split('@').first;
        isCurrentUser =
            directUsername.toLowerCase() == currentUsername.toLowerCase();
        log(
          'Current user: $currentUsername, Route user: $directUsername, Is same: $isCurrentUser',
        );
      } else {
        log('No local user data found');
      }

      // If it's the current user, go to dashboard; otherwise, portfolio
      log(
        'Navigating to ${isCurrentUser ? 'dashboard' : 'portfolio'} for user: $directUsername',
      );
      return _handleUserRoute(directUsername, isPortfolio: !isCurrentUser);
    }

    // Handle static routes
    switch (routeName) {
      case AppRoutes.login:
        return _defaultPageRoute(const LoginPage());
      case AppRoutes.register:
        return _defaultPageRoute(const RegisterPage());
      case AppRoutes.dashboard:
        return _defaultPageRoute(const AdminDashboard());
      case AppRoutes.portfolio:
        // Handle different argument types
        String? email;
        PortfolioDataModel? portfolioData;

        if (settings.arguments is String) {
          email = settings.arguments as String;
        } else if (settings.arguments is PortfolioDataModel) {
          portfolioData = settings.arguments as PortfolioDataModel;
          email = portfolioData.email;
        }

        return _defaultPageRoute(
          PortfolioMain(email: email, portfolioData: portfolioData),
        );
      default:
        return _errorRoute('No route defined for "$routeName"');
    }
  }

  static Route<dynamic> _handleUserRoute(
    String username, {
    required bool isPortfolio,
  }) {
    log(
      '_handleUserRoute called for username: $username, isPortfolio: $isPortfolio',
    );

    // Handle the navigation logic based on username
    Future.microtask(() {
      if (isPortfolio) {
        // This is a portfolio route - check database for user
        log('Handling portfolio route for username: $username');
        NavigationHelper.handleDirectUsernameRoute(username);
      } else {
        // This is a dashboard route for current user
        log('Handling dashboard route for current user: $username');
        final userData = UserDataService.getUserData();
        if (userData != null && userData['emailUser'] != null) {
          final currentUserEmail = userData['emailUser'] as String;
          final route = AppRoutes.dashboardWithUser(currentUserEmail);
          kNavigationService.clearAndNavigateTo(route);
        } else {
          // Fallback to regular dashboard
          kNavigationService.clearAndNavigateTo(AppRoutes.dashboard);
        }
      }
    });

    // Return the appropriate page immediately
    if (isPortfolio) {
      return _defaultPageRoute(const PortfolioMain());
    } else {
      return _defaultPageRoute(const AdminDashboard());
    }
  }

  static MaterialPageRoute<dynamic> _defaultPageRoute(Widget screen) {
    return MaterialPageRoute(builder: (_) => screen);
  }

  static Route<dynamic> _errorRoute(String message) {
    return MaterialPageRoute(
      builder: (_) => Scaffold(
        body: Center(
          child: Text(message, style: const TextStyle(fontSize: 18)),
        ),
      ),
    );
  }
}

<!DOCTYPE html>
<html>
<head>
    <title>Debug Routing</title>
</head>
<body>
    <h1>Debug Routing</h1>
    <p>Current URL: <span id="currentUrl"></span></p>
    <p>Pathname: <span id="pathname"></span></p>
    <p>Search: <span id="search"></span></p>
    <p>Hash: <span id="hash"></span></p>
    
    <h2>Test URLs:</h2>
    <ul>
        <li><a href="/felopaters37">Direct username: /felopaters37</a></li>
        <li><a href="/dashboard/felopaters37">Dashboard: /dashboard/felopaters37</a></li>
        <li><a href="/portfolio/felopaters37">Portfolio: /portfolio/felopaters37</a></li>
        <li><a href="/login">Login: /login</a></li>
    </ul>

    <script>
        function updateInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            document.getElementById('pathname').textContent = window.location.pathname;
            document.getElementById('search').textContent = window.location.search;
            document.getElementById('hash').textContent = window.location.hash;
        }
        
        updateInfo();
        
        // Update on navigation
        window.addEventListener('popstate', updateInfo);
        
        // Test URL extraction logic
        function extractUsernameFromRoute(route) {
            console.log('Extracting username from route:', route);
            
            if (route.startsWith('/dashboard/') || route.startsWith('/portfolio/')) {
                const parts = route.split('/');
                if (parts.length >= 3) {
                    console.log('Found username in dashboard/portfolio route:', parts[2]);
                    return parts[2];
                }
            }
            else if (route.startsWith('/') && route.length > 1) {
                const parts = route.split('/');
                if (parts.length === 2 && parts[1] !== '') {
                    const username = parts[1];
                    const staticRoutes = ['login', 'register', 'dashboard', 'portfolio'];
                    if (!staticRoutes.includes(username.toLowerCase())) {
                        console.log('Found direct username route:', username);
                        return username;
                    } else {
                        console.log('Route is a static route, not a username:', username);
                    }
                }
            }
            console.log('No username found in route:', route);
            return null;
        }
        
        // Test current URL
        const currentPath = window.location.pathname;
        const username = extractUsernameFromRoute(currentPath);
        console.log('Current path:', currentPath, 'Username:', username);
    </script>
</body>
</html>

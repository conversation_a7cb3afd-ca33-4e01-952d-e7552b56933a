import '../../../Core/Storage/Local/UserDataService/user_data_service.dart';
import '../Utils/route_utils.dart';

/// Manages user state and provides user-related utilities
class UserStateManager {
  /// Get current user data
  static Map<String, dynamic>? getCurrentUserData() {
    return UserDataService.getUserData();
  }

  /// Get current user email
  static String? getCurrentUserEmail() {
    final userData = getCurrentUserData();
    return userData?['emailUser'] as String?;
  }

  /// Get current username (part before @)
  static String? getCurrentUsername() {
    final email = getCurrentUserEmail();
    return email != null ? RouteUtils.getUsernameFromEmail(email) : null;
  }

  /// Check if user is logged in
  static bool isUserLoggedIn() {
    return getCurrentUserEmail() != null;
  }

  /// Check if given username belongs to current user
  static bool isCurrentUser(String username) {
    final currentUsername = getCurrentUsername();
    return currentUsername?.toLowerCase() == username.toLowerCase();
  }

  /// Check if user has local data
  static bool hasUserData() {
    return UserDataService.hasUserData();
  }
}
